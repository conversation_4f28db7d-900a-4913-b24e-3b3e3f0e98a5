<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout Cleanup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        #storage-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background-color: #fff;
            border: 1px solid #ddd;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Logout Cleanup Test</h1>
        <p>This test verifies that the logout process properly clears all localStorage data.</p>
        
        <div class="test-section">
            <div class="test-title">📋 Test Instructions</div>
            <div class="step">1. Click "Simulate Login Data" to create test data in localStorage</div>
            <div class="step">2. Click "Check Storage" to verify data exists</div>
            <div class="step">3. Click "Simulate Logout" to test the cleanup process</div>
            <div class="step">4. Click "Check Storage" again to verify all data is cleared</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 Test Controls</div>
            <button onclick="simulateLoginData()">Simulate Login Data</button>
            <button onclick="simulateLogout()">Simulate Logout</button>
            <button onclick="checkStorage()">Check Storage</button>
            <button onclick="clearAllStorage()">Manual Clear All</button>
            <div id="storage-status"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📊 Test Results</div>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // All localStorage keys that should be cleared on logout
        const ALL_STORAGE_KEYS = {
            // Auth keys
            USER: 'insuranceApp_user',
            REMEMBER_ME: 'insuranceApp_rememberMe',
            LAST_ACTIVITY: 'insuranceApp_lastActivity',
            SESSION_ID: 'insuranceApp_sessionId',
            
            // Dashboard keys
            DASHBOARD_STATE: 'insuranceApp_dashboardState',
            ACTIVE_TAB: 'insuranceApp_activeTab',
            CURRENT_POLICY: 'insuranceApp_currentPolicy',
            SCENARIOS: 'insuranceApp_scenarios',
            SELECTED_SCENARIOS: 'insuranceApp_selectedScenarios',
            SELECTED_CUSTOMER_DATA: 'insuranceApp_selectedCustomerData',
            SELECTED_POLICY_DATA: 'insuranceApp_selectedPolicyData',
            POLICY_SEARCH_FORM_DATA: 'insuranceApp_policySearchFormData',
            ALLOWED_ILLUSTRATION_TYPES: 'insuranceApp_allowedIllustrationTypes'
        };

        function simulateLoginData() {
            // Simulate the data that would be stored during login and app usage
            const testData = {
                [ALL_STORAGE_KEYS.USER]: JSON.stringify({
                    id: '1',
                    username: 'admin',
                    name: 'John Admin',
                    email: '<EMAIL>'
                }),
                [ALL_STORAGE_KEYS.REMEMBER_ME]: 'true',
                [ALL_STORAGE_KEYS.LAST_ACTIVITY]: Date.now().toString(),
                [ALL_STORAGE_KEYS.SESSION_ID]: 'session_' + Date.now(),
                [ALL_STORAGE_KEYS.ACTIVE_TAB]: 'selected-scenarios',
                [ALL_STORAGE_KEYS.CURRENT_POLICY]: JSON.stringify({ id: '123', name: 'Test Policy' }),
                [ALL_STORAGE_KEYS.SCENARIOS]: JSON.stringify([
                    { id: 'scenario1', name: 'Test Scenario 1' },
                    { id: 'scenario2', name: 'Test Scenario 2' }
                ]),
                [ALL_STORAGE_KEYS.SELECTED_SCENARIOS]: JSON.stringify(['scenario1']),
                [ALL_STORAGE_KEYS.SELECTED_CUSTOMER_DATA]: JSON.stringify({ name: 'John Doe', age: 35 }),
                [ALL_STORAGE_KEYS.SELECTED_POLICY_DATA]: JSON.stringify({ id: '123', type: 'Life Insurance' }),
                [ALL_STORAGE_KEYS.POLICY_SEARCH_FORM_DATA]: JSON.stringify({ searchTerm: 'test' }),
                [ALL_STORAGE_KEYS.ALLOWED_ILLUSTRATION_TYPES]: JSON.stringify(['AS-IS', 'PREMIUM'])
            };

            // Store all test data
            Object.entries(testData).forEach(([key, value]) => {
                localStorage.setItem(key, value);
            });

            updateStatus('✅ Login data simulated successfully!', 'success');
            setTimeout(checkStorage, 500);
        }

        function simulateLogout() {
            // Simulate the exact logout process from AuthContext
            updateStatus('🚪 Simulating logout process...', 'info');
            
            // Clear all application data (same as clearAllApplicationData function)
            localStorage.clear();
            sessionStorage.clear();
            
            // Double-check that specific keys are removed
            const allKeys = Object.values(ALL_STORAGE_KEYS);
            allKeys.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });
            
            updateStatus('✅ Logout simulation complete - all data should be cleared', 'success');
            setTimeout(checkStorage, 500);
        }

        function checkStorage() {
            const statusDiv = document.getElementById('storage-status');
            const resultsDiv = document.getElementById('test-results');
            
            let html = '<div class="info"><strong>Current localStorage contents:</strong><br>';
            let foundKeys = 0;
            let totalKeys = Object.keys(ALL_STORAGE_KEYS).length;
            
            // Check each expected key
            Object.entries(ALL_STORAGE_KEYS).forEach(([name, key]) => {
                const value = localStorage.getItem(key);
                if (value) {
                    foundKeys++;
                    html += `❌ ${name}: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}<br>`;
                } else {
                    html += `✅ ${name}: Not found (good!)<br>`;
                }
            });
            
            html += `</div>`;
            statusDiv.innerHTML = html;
            
            // Show all localStorage keys
            const allKeys = Object.keys(localStorage);
            html += `<br><strong>All localStorage keys (${allKeys.length}):</strong><br>`;
            if (allKeys.length === 0) {
                html += '<span class="success">✅ No keys found - localStorage is completely clean!</span>';
            } else {
                html += allKeys.map(key => `• ${key}`).join('<br>');
            }
            statusDiv.innerHTML = html;
            
            // Show test results
            let resultClass = foundKeys === 0 ? 'success' : 'error';
            let resultText = foundKeys === 0 
                ? `✅ PASS: All ${totalKeys} application keys are cleared!`
                : `❌ FAIL: ${foundKeys}/${totalKeys} application keys still exist!`;
            
            resultsDiv.innerHTML = `<div class="${resultClass}">${resultText}</div>`;
        }

        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            updateStatus('🧹 Manual clear completed', 'info');
            setTimeout(checkStorage, 500);
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Check storage on page load
        window.onload = function() {
            checkStorage();
        };
    </script>
</body>
</html>
