import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthState } from '../types';

interface AuthContextType extends AuthState {
  login: (username: string, password: string, rememberMe: boolean) => Promise<boolean>;
  logout: () => void;
  resetPassword: (email: string) => Promise<boolean>;
  isLoading: boolean;
  isLoggingOut: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  rememberMe: false,
};

type AuthAction =
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; rememberMe: boolean } }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_SESSION'; payload: { user: User; rememberMe: boolean } };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        rememberMe: false,
      };
    case 'RESTORE_SESSION':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    default:
      return state;
  }
};

// Simple session storage keys
const SESSION_KEYS = {
  USER: 'insuranceApp_user',
  REMEMBER_ME: 'insuranceApp_rememberMe',
  LAST_ACTIVITY: 'insuranceApp_lastActivity',
  SESSION_ID: 'insuranceApp_sessionId',
};

// Helper functions for session management
const clearSessionData = () => {
  localStorage.removeItem(SESSION_KEYS.USER);
  localStorage.removeItem(SESSION_KEYS.REMEMBER_ME);
  localStorage.removeItem(SESSION_KEYS.LAST_ACTIVITY);
  localStorage.removeItem(SESSION_KEYS.SESSION_ID);
};

const clearAllApplicationData = () => {
  // Clear all application data for fresh start
  localStorage.clear();
  sessionStorage.clear();

  // Double-check that specific keys are removed (in case localStorage.clear() doesn't work in some browsers)
  const allKeys = [
    ...Object.values(SESSION_KEYS),
    'insuranceApp_dashboardState',
    'insuranceApp_activeTab',
    'insuranceApp_currentPolicy',
    'insuranceApp_scenarios',
    'insuranceApp_selectedScenarios',
    'insuranceApp_selectedCustomerData',
    'insuranceApp_selectedPolicyData',
    'insuranceApp_policySearchFormData',
    'insuranceApp_allowedIllustrationTypes'
  ];

  allKeys.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  });

  console.log('🧹 All application data cleared for fresh start - localStorage and sessionStorage completely cleaned');
};

const saveSessionData = (user: User, rememberMe: boolean) => {
  const sessionId = generateSessionId();
  const timestamp = Date.now();

  localStorage.setItem(SESSION_KEYS.USER, JSON.stringify(user));
  localStorage.setItem(SESSION_KEYS.REMEMBER_ME, rememberMe.toString());
  localStorage.setItem(SESSION_KEYS.LAST_ACTIVITY, timestamp.toString());
  localStorage.setItem(SESSION_KEYS.SESSION_ID, sessionId);

  console.log('🔐 Session saved:', {
    username: user.username,
    rememberMe,
    sessionId,
    timestamp: new Date(timestamp).toISOString()
  });
};

// Generate a unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Function to detect if this is a fresh navigation vs page refresh
const detectFreshNavigation = (): boolean => {
  try {
    console.log('🔍 Detecting navigation type...');
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 Document referrer:', document.referrer || 'none');

    // Method 1: Check using PerformanceNavigationTiming API (most reliable)
    if (typeof window !== 'undefined' && window.performance && window.performance.getEntriesByType) {
      const navigationEntries = window.performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      if (navigationEntries.length > 0) {
        const navigationType = navigationEntries[0].type;
        console.log('🔍 PerformanceNavigationTiming type:', navigationType);

        if (navigationType === 'reload') {
          console.log('✅ PerformanceNavigationTiming: Page refresh detected');
          return false; // This is a page refresh
        } else if (navigationType === 'navigate') {
          console.log('✅ PerformanceNavigationTiming: Fresh navigation detected');
          return true; // This is fresh navigation
        } else if (navigationType === 'back_forward') {
          console.log('✅ PerformanceNavigationTiming: Back/forward navigation detected (treating as fresh)');
          return true;
        }
      }
    }

    // Method 2: Fallback using sessionStorage for browsers without PerformanceNavigationTiming
    // sessionStorage persists during page refresh but is cleared on new tab/window
    const sessionContinuity = sessionStorage.getItem('app_session_continuity');
    if (!sessionContinuity) {
      // No session continuity marker means this is a fresh navigation
      sessionStorage.setItem('app_session_continuity', 'active');
      console.log('✅ Session continuity fallback: Fresh navigation detected (no session marker)');
      return true;
    } else {
      console.log('✅ Session continuity fallback: Page refresh detected (session marker exists)');
      return false;
    }
  } catch (error) {
    console.log('✅ Error detecting navigation type, defaulting to fresh navigation:', error);
    return true;
  }
};

// Hardcoded credentials for demo
const DEMO_CREDENTIALS = {
  username: 'admin',
  password: 'password123',
  user: {
    id: '1',
    username: 'admin',
    name: 'John Admin',
    email: '<EMAIL>',
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);

  useEffect(() => {
    // Enhanced session restoration logic with fresh navigation detection
    const restoreSession = () => {
      console.log('🔍 Checking for existing session...');

      // Detect if this is a fresh navigation vs page refresh
      const isFreshNavigation = detectFreshNavigation();
      console.log('🔍 Navigation type:', isFreshNavigation ? 'Fresh localhost link' : 'Page refresh');

      const savedUser = localStorage.getItem(SESSION_KEYS.USER);
      const savedRememberMe = localStorage.getItem(SESSION_KEYS.REMEMBER_ME);

      // Check if we have session data
      if (savedUser) {
        try {
          const user = JSON.parse(savedUser);
          const rememberMe = savedRememberMe === 'true';

          // If this is a fresh navigation (localhost link click), clear session and start fresh
          if (isFreshNavigation) {
            console.log('🆕 Fresh localhost navigation detected - clearing session for fresh start');
            clearAllApplicationData();
            console.log('✅ Session cleared, redirecting to login page');
          } else {
            // This is a page refresh - restore the session and update activity
            console.log('🔄 Page refresh detected - restoring session:', {
              username: user.username,
              rememberMe
            });

            // Update last activity timestamp for page refresh
            const timestamp = Date.now();
            localStorage.setItem(SESSION_KEYS.LAST_ACTIVITY, timestamp.toString());

            dispatch({
              type: 'RESTORE_SESSION',
              payload: {
                user,
                rememberMe,
              }
            });
          }
        } catch (error) {
          console.log('❌ Error parsing saved user data, clearing session');
          clearSessionData();
        }
      } else {
        console.log('❌ No saved session found');
      }

      // Set loading to false after checking session
      setIsLoading(false);
    };

    restoreSession();
  }, []);

  const login = async (username: string, password: string, rememberMe: boolean): Promise<boolean> => {
    console.log('🔐 Login attempt:', { username, rememberMe });

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password) {
      console.log('✅ Login successful');

      // Clear all old application data for fresh start - ensures no previous user data persists
      clearAllApplicationData();

      // Update state
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: DEMO_CREDENTIALS.user,
          rememberMe,
        }
      });

      // Save session data for persistence across refreshes
      saveSessionData(DEMO_CREDENTIALS.user, rememberMe);

      console.log('✅ Login complete - fresh session established');
      return true;
    }

    console.log('❌ Login failed: Invalid credentials');
    return false;
  };

  const logout = () => {
    console.log('🚪 Logging out user');

    // Set logging out state for smooth transition
    setIsLoggingOut(true);

    // Small delay for smooth UX
    setTimeout(() => {
      // Clear all application data for fresh start - this ensures complete cleanup
      clearAllApplicationData();

      // Update authentication state to redirect to login
      dispatch({ type: 'LOGOUT' });

      // Reset logging out state
      setIsLoggingOut(false);

      console.log('✅ Logout complete, all data cleared, redirecting to login');
    }, 300); // 300ms delay for smooth transition
  };

  const resetPassword = async (_email: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return true;
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      resetPassword,
      isLoading,
      isLoggingOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};