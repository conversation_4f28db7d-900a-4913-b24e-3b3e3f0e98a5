import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import Notification from '../common/Notification';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  saveInterestRateIllustration,
  validateInterestRateData,
  type InterestRateIllustrationData
} from '../../services/interestRateService';

// Extended interface to support comprehensive rate data storage
interface ExtendedInterestRateData extends InterestRateIllustrationData {
  current_rate?: number;
  guaranteed_rate?: number;
  selected_scenarios?: {
    current_rate_selected: boolean;
    guaranteed_rate_selected: boolean;
    stress_rate_selected: boolean;
    user_defined_selected: boolean;
  };
}

const InterestRatePage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, loadScenariosFromBackend } = useDashboard();

  // Scenario state
  const [stressRate, setStressRate] = useState('');
  const [stressRateEnabled, setStressRateEnabled] = useState(false);

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);

  // State for user-defined rates functionality (following Face Amount page pattern)
  const [userDefinedRatesData, setUserDefinedRatesData] = useState({
    enabled: false,
    selectedTypes: {
      current: false,
      guaranteed: false,
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40,
      end: 100
    },
    policyYearRange: {
      start: 1,
      end: 100
    },
    calendarYearRange: {
      start: 2024,
      end: 2100
    },
    isEditing: false,
    tableData: [] as TableRowData[]
  });

  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    interestRate: number;
  };

  // Get current interest rate from policy data - FIXED to return number
  const getCurrentInterestRate = (): { display: string; value: number } => {
    const policyData = selectedPolicyData as any;
    if (policyData?.CURRENT_INTEREST_RATE_IN_PERCENTAGE) {
      const value = Number(policyData.CURRENT_INTEREST_RATE_IN_PERCENTAGE);
      return {
        display: `${value}%`,
        value: value
      };
    }
    return { display: 'N/A', value: 0 };
  };

  // Get guaranteed minimum rate from policy data - FIXED to return number
  const getGuaranteedMinimumRate = (): { display: string; value: number } => {
    const policyData = selectedPolicyData as any;
    if (policyData?.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE) {
      const value = Number(policyData.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE);
      return {
        display: `${value}%`,
        value: value
      };
    }
    return { display: 'N/A', value: 0 };
  };

  // Calculate current age from DOB - FIXED for Safari compatibility
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40;

    const dob = selectedCustomerData.details.DOB;
    let birthDate: Date;

    try {
      if (dob.includes('.')) {
        const dobParts = dob.split('.');
        if (dobParts.length !== 3) return 40;
        const [day, month, year] = dobParts.map(part => parseInt(part, 10));
        // Validate parsed values
        if (isNaN(day) || isNaN(month) || isNaN(year)) return 40;
        birthDate = new Date(year, month - 1, day);
      } else if (dob.includes('/')) {
        const dobParts = dob.split('/');
        if (dobParts.length !== 3) return 40;
        const [first, second, year] = dobParts.map(part => parseInt(part, 10));
        // Validate parsed values
        if (isNaN(first) || isNaN(second) || isNaN(year)) return 40;
        birthDate = new Date(year, first - 1, second);
      } else if (dob.includes('-')) {
        birthDate = new Date(dob);
        // Validate date
        if (isNaN(birthDate.getTime())) return 40;
      } else {
        return 40;
      }

      // Validate birthDate
      if (isNaN(birthDate.getTime())) return 40;

      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      // Ensure age is a valid number and within reasonable range
      const validAge = Math.max(0, Math.min(150, age));
      return isNaN(validAge) ? 40 : validAge;
    } catch (error) {
      console.error('Error calculating age:', error);
      return 40;
    }
  };

  // Calculate current policy year from issue date - FIXED for Safari compatibility
  const calculateCurrentPolicyYear = (): number => {
    try {
      const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
      if (issueDate) {
        const issue = new Date(issueDate);
        // Validate issue date
        if (isNaN(issue.getTime())) return 1;
        
        const today = new Date();
        const yearsDiff = today.getFullYear() - issue.getFullYear();
        const monthsDiff = today.getMonth() - issue.getMonth();
        const daysDiff = today.getDate() - issue.getDate();

        let totalMonths = yearsDiff * 12 + monthsDiff;
        if (daysDiff >= 0) {
          totalMonths += 1;
        }

        const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
        // Ensure policy year is a valid number
        return isNaN(policyYear) ? 1 : Math.max(1, Math.min(100, policyYear));
      }
      return 1;
    } catch (error) {
      console.error('Error calculating policy year:', error);
      return 1;
    }
  };

  // Get current calendar year - FIXED for Safari compatibility
  const getCurrentYear = (): number => {
    try {
      const year = new Date().getFullYear();
      return isNaN(year) ? 2024 : year;
    } catch (error) {
      console.error('Error getting current year:', error);
      return 2024;
    }
  };

  // Initialize ranges with actual values - FIXED with proper validation
  useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    // Validate all values before setting
    const validCurrentAge = isNaN(currentAge) ? 40 : currentAge;
    const validCurrentPolicyYear = isNaN(currentPolicyYear) ? 1 : currentPolicyYear;
    const validCurrentYear = isNaN(currentYear) ? 2024 : currentYear;

    setUserDefinedRatesData(prev => ({
      ...prev,
      ageRange: {
        start: validCurrentAge,
        end: 100
      },
      policyYearRange: {
        start: validCurrentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: validCurrentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data based on selected types and ranges
  const generateTableData = (): TableRowData[] => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = userDefinedRatesData;

    let startYear = 0;
    let endYear = 0;

    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }

    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        interestRate: 5.0 // Default interest rate
      };

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  useEffect(() => {
    const newTableData = generateTableData();
    setUserDefinedRatesData(prev => ({ ...prev, tableData: newTableData }));
  }, [userDefinedRatesData.selectedTypes, userDefinedRatesData.ageRange, userDefinedRatesData.policyYearRange, userDefinedRatesData.calendarYearRange]);

  // Save scenario function - FIXED to save all selected rate values as answers
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      setNotification({ message: 'Please select a customer and policy first!', type: 'error' });
      return;
    }

    // Validate that at least one option is selected
    const hasCurrentRate = userDefinedRatesData.selectedTypes.current;
    const hasGuaranteedRate = userDefinedRatesData.selectedTypes.guaranteed;
    // ✅ FIX: Allow stress rate of 0 - check if enabled and has any value (including 0)
    const hasStressRate = stressRateEnabled && (stressRate !== '' && stressRate !== null && stressRate !== undefined);
    const hasUserDefinedRate = userDefinedRatesData.enabled && userDefinedRatesData.tableData.length > 0;

    console.log('🔍 Interest Rate validation:', {
      hasCurrentRate,
      hasGuaranteedRate,
      hasStressRate,
      hasUserDefinedRate,
      stressRateEnabled,
      stressRate
    });

    if (!hasCurrentRate && !hasGuaranteedRate && !hasStressRate && !hasUserDefinedRate) {
      setNotification({ message: 'Please select at least one interest rate option before saving!', type: 'error' });
      return;
    }

    setIsSaving(true);
    try {
      // Get policy ID from selected policy data
      const policyId = parseInt(selectedPolicyData.id) || parseInt(selectedCustomerData.customerId);

      if (!policyId) {
        throw new Error('Policy ID not found in selected data');
      }

      // Get the actual rate values from policy data
      const currentRateData = getCurrentInterestRate();
      const guaranteedRateData = getGuaranteedMinimumRate();

      // Prepare comprehensive data for backend API - Save ALL answers/values
      const interestRateData: ExtendedInterestRateData = {
        policy_id: policyId,

        // Always save the current and guaranteed rates as reference values
        current_rate: currentRateData.value,
        guaranteed_rate: guaranteedRateData.value,

        // Selected scenario details
        selected_scenarios: {
          current_rate_selected: hasCurrentRate,
          guaranteed_rate_selected: hasGuaranteedRate,
          stress_rate_selected: hasStressRate,
          user_defined_selected: hasUserDefinedRate
        },

        // Stress rate if selected
        ...(hasStressRate && {
          stress_rate: parseFloat(stressRate)
        }),

        // User-defined rate schedule if selected
        ...(hasUserDefinedRate && {
          schedule_data: userDefinedRatesData.tableData.map(row => ({
            age: row.age,
            policy_year: parseInt(row.policyYear.replace('Year ', '')),
            calendar_year: row.calendarYear,
            interest_rate: row.interestRate
          })),
          age_range: userDefinedRatesData.selectedTypes.age ? userDefinedRatesData.ageRange : undefined,
          policy_year_range: userDefinedRatesData.selectedTypes.policyYear ? userDefinedRatesData.policyYearRange : undefined,
          calendar_year_range: userDefinedRatesData.selectedTypes.calendarYear ? userDefinedRatesData.calendarYearRange : undefined,
          selected_type: userDefinedRatesData.selectedTypes.age ? 'age' :
                        userDefinedRatesData.selectedTypes.policyYear ? 'policyYear' :
                        userDefinedRatesData.selectedTypes.calendarYear ? 'calendarYear' : undefined
        }),

        // Primary rate type for processing
        rate_type: hasCurrentRate ? 'current' :
                  hasGuaranteedRate ? 'guaranteed' :
                  hasStressRate ? 'stress' :
                  hasUserDefinedRate ? 'user-defined' : 'current'
      };

      console.log('🔍 Prepared Interest Rate data for saving:', interestRateData);

      // Validate data before sending
      const validationErrors = validateInterestRateData(interestRateData);
      if (validationErrors.length > 0) {
        setNotification({ message: `Validation errors: ${validationErrors.join(', ')}`, type: 'error' });
        return;
      }

      // Calculate current age and policy year (you may need to adjust this based on your data)
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();

      // Save to backend
      const result = await saveInterestRateIllustration(interestRateData, currentAge, currentPolicyYear);

      if (result.status === 'SUCCESS') {
        setNotification({ message: 'Interest Rate illustration is saved successfully', type: 'success' });

        // ✅ Reload scenarios from database to get the real saved scenarios with database IDs
        try {
          if (selectedPolicyData?.id) {
            console.log('🔄 Reloading scenarios from database after Interest Rate save...');
            await loadScenariosFromBackend(parseInt(selectedPolicyData.id));
            setNotification({ message: 'Interest Rate illustration saves successfully!', type: 'success' });
            console.log('✅ Interest Rate scenarios saved and reloaded from database');
          }
        } catch (error) {
          console.error('❌ Error reloading scenarios from database:', error);
          setNotification({ message: 'Scenarios saved but error loading from database', type: 'error' });
        }
      } else {
        throw new Error(result.message || 'Failed to save to database');
      }

    } catch (error) {
      console.error('Error saving scenario:', error);
      setNotification({ message: `Error saving scenario: ${error instanceof Error ? error.message : 'Please try again.'}`, type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    setStressRate('');
    setStressRateEnabled(false);
    setUserDefinedRatesData(prev => ({
      ...prev,
      enabled: false,
      selectedTypes: {
        current: false,
        guaranteed: false,
        age: false,
        policyYear: false,
        calendarYear: false
      },
      isEditing: false,
      tableData: []
    }));
    setNotification({ message: 'All interest rate scenarios have been reset!', type: 'success' });
  };

  // Safe increment/decrement functions for Safari compatibility
  const safeIncrement = (value: number, min: number, max: number): number => {
    const newValue = (value || min) + 1;
    return Math.min(max, Math.max(min, newValue));
  };

  const safeDecrement = (value: number, min: number, max: number): number => {
    const newValue = (value || min) - 1;
    return Math.min(max, Math.max(min, newValue));
  };

  return (
    <div className="space-y-6">
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Interest Rate illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Description Section - match AsIsPage style */}
          <Card className="bg-blue-50 border-blue-200">
            <div className="p-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                These scenarios show how your policy performs under different interest rate environments—typically Current, Guaranteed, and Alternative Rates. This helps you assess risk and understand how policy values may fluctuate with market conditions.
              </p>
            </div>
          </Card>
          {/* Interest Rate Scenarios - Four Main Options */}
          <Card className="mb-8">
           {/* <h2 className="text-xl font-bold mb-6 text-black">Interest Rate Scenarios</h2> */}
            <div className="space-y-6">
              {/* Unified Options Container */}
              <div className="bg-white p-6 rounded-lg border-2 border-gray-300 space-y-6">
                {/* Question text */}
                <h3 className="text-lg font-semibold text-black mb-4">Do you want to model different interest / crediting rate scenarios for the policy?</h3>
                {/* Radio group for Current and Guaranteed rates */}
                <div className="flex flex-col gap-4">
                  {/* Option 1: Current Interest Rate */}
                  <div className="flex items-center justify-between">
                    <label className="flex items-center text-lg font-semibold text-black">
                      <input
                        type="radio"
                        checked={userDefinedRatesData.selectedTypes.current}
                        onChange={() => {
                          setUserDefinedRatesData(prev => ({
                            ...prev,
                            selectedTypes: {
                              current: !prev.selectedTypes.current,
                              guaranteed: false,
                              age: false,
                              policyYear: false,
                              calendarYear: false
                            },
                            enabled: false
                          }));
                          setStressRateEnabled(false);
                        }}
                        className="mr-2"
                      />
                      Current interest/crediting rate
                    </label>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-blue-600">
                        {getCurrentInterestRate().display}
                      </span>
                    </div>
                  </div>

                  {/* Option 2: Guaranteed Minimum Rate */}
                  <div className="flex items-center justify-between">
                    <label className="flex items-center text-lg font-semibold text-black">
                      <input
                        type="radio"
                        checked={userDefinedRatesData.selectedTypes.guaranteed}
                        onChange={() => {
                          setUserDefinedRatesData(prev => ({
                            ...prev,
                            selectedTypes: {
                              current: false,
                              guaranteed: !prev.selectedTypes.guaranteed,
                              age: false,
                              policyYear: false,
                              calendarYear: false
                            },
                            enabled: false
                          }));
                          setStressRateEnabled(false);
                        }}
                        className="mr-2"
                      />
                      Guaranteed minimum rate
                    </label>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-green-600">
                        {getGuaranteedMinimumRate().display}
                      </span>
                    </div>
                  </div>
                </div>
                {/* Option 3: Stress scenario rate */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="flex items-center text-lg font-semibold text-black">
                      <input
                        type="radio"
                        checked={stressRateEnabled}
                        onChange={() => {
                          setStressRateEnabled(!stressRateEnabled);
                          if (stressRateEnabled) setStressRate('');
                          setUserDefinedRatesData(prev => ({
                            ...prev,
                            selectedTypes: {
                              current: false,
                              guaranteed: false,
                              age: false,
                              policyYear: false,
                              calendarYear: false
                            },
                            enabled: false
                          }));
                        }}
                        className="mr-2"
                      />
                      Stress scenario rate
                    </label>
                    {stressRate && (
                      <div className="text-right">
                        <span className="text-2xl font-bold text-red-600">
                          {stressRate}%
                        </span>
                      </div>
                    )}
                  </div>
                  {stressRateEnabled && (
                    <div className="ml-6">
                      <label className="block text-sm font-bold text-black mb-1">
                        Stress Rate Percentage:
                      </label>
                      <Input
                        value={stressRate}
                        onChange={e => setStressRate(e.target.value)}
                        placeholder="Enter stress scenario rate"
                        className="text-black placeholder-gray-500 w-64"
                      />
                    </div>
                  )}
                </div>
                {/* User-defined interest rate Schedule - Checkbox */}
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="radio"
                    checked={userDefinedRatesData.enabled}
                    onChange={() => {
                      setUserDefinedRatesData(prev => ({
                        ...prev,
                        enabled: !prev.enabled,
                        selectedTypes: {
                          current: false,
                          guaranteed: false,
                          age: false,
                          policyYear: false,
                          calendarYear: false
                        }
                      }));
                      setStressRateEnabled(false);
                    }}
                    className="mr-2"
                  />
                  User-defined interest rate Schedule
                </label>
                {userDefinedRatesData.enabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-gray-50 p-6 rounded-lg border-2 border-gray-300">
                      {/* Type Selection Checkboxes - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={userDefinedRatesData.selectedTypes.age}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: e.target.checked,
                                policyYear: false,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={userDefinedRatesData.selectedTypes.policyYear}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: false,
                                policyYear: e.target.checked,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={userDefinedRatesData.selectedTypes.calendarYear}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: false,
                                policyYear: false,
                                calendarYear: e.target.checked
                              }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                          {/* Age Range Toggle Bars - FIXED for Safari */}
                          {userDefinedRatesData.selectedTypes.age && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { 
                                          ...prev.ageRange, 
                                          start: safeDecrement(prev.ageRange.start, calculateCurrentAge(), 100)
                                        }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.ageRange.start || calculateCurrentAge()}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { 
                                          ...prev.ageRange, 
                                          start: safeIncrement(prev.ageRange.start, calculateCurrentAge(), 100)
                                        }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">End Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { 
                                          ...prev.ageRange, 
                                          end: safeDecrement(prev.ageRange.end, prev.ageRange.start || calculateCurrentAge(), 100)
                                        }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.ageRange.end || 100}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { 
                                          ...prev.ageRange, 
                                          end: safeIncrement(prev.ageRange.end, prev.ageRange.start || calculateCurrentAge(), 100)
                                        }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Policy Year Range Toggle Bars - FIXED for Safari */}
                          {userDefinedRatesData.selectedTypes.policyYear && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { 
                                          ...prev.policyYearRange, 
                                          start: safeDecrement(prev.policyYearRange.start, calculateCurrentPolicyYear(), 100)
                                        }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.policyYearRange.start || calculateCurrentPolicyYear()}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { 
                          ...prev.policyYearRange, 
                          start: safeIncrement(prev.policyYearRange.start, calculateCurrentPolicyYear(), 100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ▶
                    </button>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        policyYearRange: { 
                          ...prev.policyYearRange, 
                          end: safeDecrement(prev.policyYearRange.end, prev.policyYearRange.start || calculateCurrentPolicyYear(), 100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ◀
                    </button>
                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                      {userDefinedRatesData.policyYearRange.end || 100}
                    </span>
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        policyYearRange: { 
                          ...prev.policyYearRange, 
                          end: safeIncrement(prev.policyYearRange.end, prev.policyYearRange.start || calculateCurrentPolicyYear(), 100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ▶
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Calendar Year Range Toggle Bars - FIXED for Safari */}
          {userDefinedRatesData.selectedTypes.calendarYear && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        calendarYearRange: { 
                          ...prev.calendarYearRange, 
                          start: safeDecrement(prev.calendarYearRange.start, getCurrentYear(), 2100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ◀
                    </button>
                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                      {userDefinedRatesData.calendarYearRange.start || getCurrentYear()}
                    </span>
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        calendarYearRange: { 
                          ...prev.calendarYearRange, 
                          start: safeIncrement(prev.calendarYearRange.start, getCurrentYear(), 2100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ▶
                    </button>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        calendarYearRange: { 
                          ...prev.calendarYearRange, 
                          end: safeDecrement(prev.calendarYearRange.end, prev.calendarYearRange.start || getCurrentYear(), 2100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ◀
                    </button>
                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                      {userDefinedRatesData.calendarYearRange.end || 2100}
                    </span>
                    <button
                      onClick={() => setUserDefinedRatesData(prev => ({
                        ...prev,
                        calendarYearRange: { 
                          ...prev.calendarYearRange, 
                          end: safeIncrement(prev.calendarYearRange.end, prev.calendarYearRange.start || getCurrentYear(), 2100)
                        }
                      }))}
                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                    >
                      ▶
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
                    </div>

                    {/* Buttons Row - moved outside container and above table */}
                    <div className="flex justify-between items-center mt-6 mb-4">
                      <button
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                      >
                        View Year by Year Details
                      </button>
                      <button
                        onClick={() => setUserDefinedRatesData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                      >
                        {userDefinedRatesData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                      </button>
                    </div>

                    {/* Data Table */}
                    <div className="mt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Interest Rate (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {userDefinedRatesData.tableData.length === 0 ? (
                              <tr>
                                <td
                                  colSpan={4}
                                  className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                >
                                  Select year range to populate table
                                </td>
                              </tr>
                            ) : (
                              userDefinedRatesData.tableData.map((row, index) => (
                                <tr key={index}>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.age}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.policyYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.calendarYear}</td>
                                  <td className="border border-gray-300 px-4 py-2">
                                    <input
                                      type="number"
                                      value={row.interestRate}
                                      readOnly={!userDefinedRatesData.isEditing}
                                      onChange={(e) => {
                                        if (userDefinedRatesData.isEditing) {
                                          const newTableData = [...userDefinedRatesData.tableData];
                                          newTableData[index].interestRate = parseFloat(e.target.value) || 0;
                                          setUserDefinedRatesData(prev => ({ ...prev, tableData: newTableData }));
                                        }
                                      }}
                                      className={`w-full p-2 border rounded text-black ${
                                        userDefinedRatesData.isEditing
                                          ? 'border-gray-300 bg-white'
                                          : 'border-gray-300 bg-gray-100'
                                      }`}
                                      step="0.1"
                                    />
                                  </td>
                                </tr>
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-black hover:bg-gray-800 text-white shadow-lg border-none"
              disabled={isSaving}
            >
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Interest Rate Illustration'}</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-black hover:bg-gray-800 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default InterestRatePage;